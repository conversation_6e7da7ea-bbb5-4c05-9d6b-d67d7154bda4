@use './common/var.scss' as *;

// ===== FieldContainer 基础变量 =====
:root {
  --sp-color-white: #ffffff;
}

// ===== FieldContainer 主样式 =====
.sp-field-container {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;

  // ===== 包装器 =====
  &__wrapper {
    position: relative;
    display: flex;
    align-items: stretch;
    width: 100%;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    cursor: text;
    min-height: 48px;
    overflow: visible;
    box-sizing: border-box;
  }

  // ===== 浮动标签 =====
  &__label {
    position: absolute;
    left: 12px;
    top: 16px;
    color: $color-text-secondary;
    font-size: 16px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    transform-origin: left top;
    z-index: 1;
    user-select: none;
    cursor: text;
    background: $background-color-base;
    padding: 0 4px;
    border-radius: 4px;
    white-space: nowrap;

    // 必填星号
    &-asterisk {
      color: $color-danger;
      margin-right: 2px;
    }

    // 浮动状态
    &--floating {
      top: 0;
      transform: translateY(-50%) scale(0.85);
      color: $color-text-secondary;
      font-weight: 500;
    }
  }

  // ===== 聚焦状态下的标签颜色 =====
  &--focused {
    .sp-field-container__label {
      color: $color-primary;
    }
  }

  // ===== 输入元素基础样式 =====
  &__input {
    flex: 1;
    width: 100%;
    padding: 16px 12px;
    border: none;
    outline: none;
    background: transparent;
    color: $color-text-primary;
    font-size: 16px;
    font-family: inherit;
    line-height: 1.5;
    transition: none;
    transform: none;
    will-change: auto;
    position: relative;
    z-index: 1;
    box-sizing: border-box;

    &::placeholder {
      color: $color-text-secondary;
      opacity: 0.8;
    }

    &:disabled {
      color: $color-text-disabled;
      cursor: not-allowed;

      &::placeholder {
        color: $color-text-disabled;
      }
    }

    &:read-only {
      cursor: default;
    }
  }

  // ===== 功能区域 =====
  &__functions {
    position: absolute;
    top: 16px;
    right: 16px;
    bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-start;
    gap: 8px;
    color: $color-text-secondary;
    pointer-events: none;
    z-index: 2;

    // 功能元素可交互
    > * {
      pointer-events: auto;
    }
  }

  // ===== 加载动画 =====
  &__loading-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    overflow: hidden;
    background: transparent;
  }

  &__loading-progress {
    height: 100%;
    background: $color-primary;
    width: 30%;
    animation: sp-field-container-loading 1.5s infinite ease-in-out;
  }

  // ===== 消息区域 =====
  &__message,
  &__helper {
    margin-top: 6px;
    font-size: 12px;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  &__message {
    color: $color-danger;

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  &__helper {
    color: $color-text-secondary;

    &--error {
      color: $color-danger;
    }

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  // ===== 尺寸变体 =====
  &--small {
    .sp-field-container__wrapper {
      min-height: 40px;
    }

    .sp-field-container__input {
      padding: 12px 10px;
      font-size: 14px;
    }

    .sp-field-container__label {
      left: 10px;
      top: 12px;
      font-size: 14px;

      &--floating {
        transform: translateY(-45%) scale(0.8);
      }
    }

    .sp-field-container__functions {
      top: 12px;
      right: 12px;
    }

    // 小尺寸填充变体的特殊处理
    &.sp-field-container--filled {
      .sp-field-container__input {
        padding-top: 20px;
        padding-bottom: 8px;
      }

      .sp-field-container__label {
        top: 8px;

        &--floating {
          top: 6px;
        }
      }
    }
  }

  &--large {
    .sp-field-container__wrapper {
      min-height: 56px;
    }

    .sp-field-container__input {
      padding: 20px 14px;
      font-size: 18px;
    }

    .sp-field-container__label {
      left: 14px;
      top: 20px;
      font-size: 18px;

      &--floating {
        transform: translateY(-55%) scale(0.9);
      }
    }

    .sp-field-container__functions {
      top: 20px;
      right: 20px;
    }

    // 大尺寸填充变体的特殊处理
    &.sp-field-container--filled {
      .sp-field-container__input {
        padding-top: 32px;
        padding-bottom: 16px;
      }

      .sp-field-container__label {
        top: 16px;

        &--floating {
          top: 12px;
        }
      }
    }
  }

  // ===== 变体样式 =====

  // 默认变体
  &--default {
    .sp-field-container__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 6px;
      background: var(--sp-color-white);
    }

    .sp-field-container__label {
      background: var(--sp-color-white);
      padding: 0 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // ===== 下划线变体 =====
  &--underlined {
    .sp-field-container__wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 0;
      background: transparent;
    }

    .sp-field-container__label {
      background: transparent;
      padding: 0;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        border-bottom-color: $color-primary;
        box-shadow: 0 1px 0 0 $color-primary;
      }
    }
  }

  // ===== 填充变体 =====
  &--filled {
    .sp-field-container__wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 6px 6px 0 0;
      background: $background-color-hover;
    }

    .sp-field-container__input {
      padding-top: 36px; // 更大的顶部内边距，让输入区域更往下
      padding-bottom: 12px;
    }

    .sp-field-container__label {
      background: transparent;
      padding: 0;
      top: 12px;

      &--floating {
        top: 8px;
        transform: translateY(0) scale(0.85);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        border-bottom-color: $color-primary;
        background: $background-color-base;
        box-shadow: 0 1px 0 0 $color-primary;
      }
    }
  }

  // ===== 胶囊变体 =====
  &--pill {
    .sp-field-container__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 50px;
      background: var(--sp-color-white);
    }

    .sp-field-container__label {
      background: var(--sp-color-white);
      padding: 0 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // ===== 方形变体 =====
  &--square {
    .sp-field-container__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 0;
      background: var(--sp-color-white);
    }

    .sp-field-container__label {
      background: var(--sp-color-white);
      padding: 0 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // ===== 无边框变体 =====
  &--unborder {
    .sp-field-container__wrapper {
      border: none;
      border-radius: 6px;
      background: transparent;
    }

    .sp-field-container__label {
      background: transparent;
      padding: 0;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        background: $background-color-hover;
      }
    }

    &:hover {
      .sp-field-container__wrapper {
        background: rgba($background-color-hover, 0.5);
      }
    }
  }

  // ===== 状态样式 =====
  &--error {
    .sp-field-container__wrapper {
      border-color: $color-danger !important;
    }

    .sp-field-container__label {
      color: $color-danger !important;
    }

    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2) !important;
      }
    }
  }

  &--disabled {
    .sp-field-container__wrapper {
      background: $background-color-disabled;
      border-color: $border-color-disabled;
      cursor: not-allowed;
    }

    .sp-field-container__label {
      color: $color-text-disabled;
    }
  }

  // ===== 发光效果 =====
  &--effect-glow {
    &.sp-field-container--focused {
      .sp-field-container__wrapper {
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2),
          0 4px 12px rgba($color-primary, 0.15);
      }
    }

    &.sp-field-container--error.sp-field-container--focused {
      .sp-field-container__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2),
          0 4px 12px rgba($color-danger, 0.15);
      }
    }
  }
}

// ===== Textarea 特有样式 =====
.sp-textarea-field {
  &__textarea {
    // 继承 field-container 的输入样式，添加 textarea 特有属性
    min-height: inherit;
    resize: vertical; // 默认垂直调整大小
    font-family: inherit;
    line-height: 1.5;
  }

  &__clear {
    color: $color-text-secondary;
    cursor: pointer;
    transition: color 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    padding: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      color: $color-primary;
      background: rgba(255, 255, 255, 1);
      border-color: $color-primary;
    }
  }

  &__count {
    font-size: 11px;
    color: $color-text-secondary;
    white-space: nowrap;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 2px 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 4px;
  }
}

// ===== 动画 =====
@keyframes sp-field-container-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(350%);
  }
}

// ===== 消息过渡动画 =====
.sp-field-container-message-enter-active,
.sp-field-container-message-leave-active {
  transition: all 0.3s ease;
}

.sp-field-container-message-enter-from,
.sp-field-container-message-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
