@use './common/var.scss' as *;

// ===== TextareaField 基础变量 =====
:root {
  --sp-color-white: #ffffff;
}

// ===== TextareaField 主样式 =====
.sp-textarea-field {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;

  // ===== 包装器 =====
  &__wrapper {
    position: relative;
    display: flex;
    align-items: stretch; // 改为 stretch 以适应 textarea 高度
    width: 100%;
    transition: border-color 0.3s ease, box-shadow 0.3s ease; // 只对边框和阴影应用过渡
    cursor: text;
    min-height: 80px; // 为 textarea 设置最小高度
  }

  // ===== 浮动标签 =====
  &__label {
    position: absolute;
    left: 12px;
    top: 16px; // 调整为适合 textarea 的位置
    color: $color-text-secondary;
    font-size: 16px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    transform-origin: left top;
    z-index: 1;
    user-select: none;
    cursor: text;
    background: $background-color-base;
    padding: 0 4px;
    border-radius: 4px;
    white-space: nowrap;

    // 必填星号
    &-asterisk {
      color: $color-danger;
      margin-right: 2px;
    }

    // 浮动状态
    &--floating {
      top: 0;
      transform: translateY(-50%) scale(0.85);
      color: $color-text-secondary;
      font-weight: 500;
    }
  }

  // ===== 聚焦状态下的标签颜色 =====
  &--focused {
    .sp-textarea-field__label {
      color: $color-primary;
    }
  }

  // ===== 文本域内核 =====
  &__inner {
    flex: 1;
    width: 100%;
    padding: 16px 12px;
    border: none;
    outline: none;
    background: transparent;
    color: $color-text-primary;
    font-size: 16px;
    font-family: inherit;
    line-height: 1.5;
    resize: vertical; // 默认允许垂直调整大小
    min-height: 48px; // 设置最小高度

    // 移除所有可能影响拖拽性能的样式
    transition: none; // 完全移除过渡动画
    transform: none; // 确保没有变换
    will-change: auto; // 重置 will-change

    // 确保拖拽图标区域可用
    position: relative;

    &::placeholder {
      color: $color-text-secondary;
      opacity: 0.8;
    }

    &:disabled {
      color: $color-text-disabled;
      cursor: not-allowed;
      resize: none;

      &::placeholder {
        color: $color-text-disabled;
      }
    }

    &:read-only {
      resize: none;
    }

    // 确保拖拽手柄可见且可操作
    &::-webkit-resizer {
      background: transparent;
      border: none;
    }
  }

  // ===== 前缀/后缀区域 =====
  &__prefix,
  &__suffix {
    display: flex;
    align-items: flex-start; // 改为顶部对齐，适合 textarea
    gap: 6px;
    color: $color-text-secondary;
    flex-shrink: 0;
    padding-top: 16px; // 与 textarea 内容对齐
  }

  &__prefix {
    padding-left: 12px;
    padding-right: 6px;
  }

  &__suffix {
    padding-right: 12px;
    padding-left: 6px;
    // 简化后缀区域布局
    flex-direction: row; // 保持水平排列
    align-items: flex-start;
    justify-content: flex-end;
    flex-wrap: wrap; // 允许换行
    gap: 6px;
  }

  // ===== 图标样式 =====
  &__prefix-icon,
  &__suffix-icon,
  &__clear {
    color: $color-text-secondary;
    transition: color 0.3s ease;

    &:hover {
      color: $color-primary;
    }
  }

  &__clear {
    cursor: pointer;

    &:hover {
      color: $color-primary;
    }

    // 在有后缀的情况下添加背景
    .sp-textarea-field--has-suffix & {
      background: rgba(255, 255, 255, 0.95);
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      padding: 4px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      &:hover {
        background: rgba(255, 255, 255, 1);
        border-color: $color-primary;
      }
    }
  }

  // ===== 字数统计 =====
  &__count {
    font-size: 12px;
    color: $color-text-secondary;
    white-space: nowrap;
    margin-bottom: 4px; // 与其他后缀元素的间距

    // 在有后缀的情况下添加背景
    .sp-textarea-field--has-suffix & {
      background: rgba(255, 255, 255, 0.95);
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 11px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  // ===== 加载动画 =====
  &__loading-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    overflow: hidden;
    background: transparent;
  }

  &__loading-progress {
    height: 100%;
    background: $color-primary;
    width: 30%;
    animation: sp-textarea-field-loading 1.5s infinite ease-in-out;
  }

  // ===== 消息区域 =====
  &__message,
  &__helper {
    margin-top: 6px;
    font-size: 12px;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  &__message {
    color: $color-danger;

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  &__helper {
    color: $color-text-secondary;

    &--error {
      color: $color-danger;
    }

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  // ===== 尺寸变体 =====
  &--small {
    .sp-textarea-field__wrapper {
      min-height: 60px;
    }

    .sp-textarea-field__inner {
      padding: 12px 10px;
      font-size: 14px;
      min-height: 36px;
    }

    .sp-textarea-field__label {
      left: 10px;
      top: 12px;
      font-size: 14px;

      &--floating {
        transform: translateY(-45%) scale(0.8);
      }
    }

    .sp-textarea-field__prefix,
    .sp-textarea-field__suffix {
      padding-top: 12px;
    }

    .sp-textarea-field__prefix {
      padding-left: 10px;
      padding-right: 5px;
    }

    .sp-textarea-field__suffix {
      padding-right: 10px;
      padding-left: 5px;
    }

    .sp-textarea-field__count {
      padding-bottom: 12px;
    }
  }

  &--large {
    .sp-textarea-field__wrapper {
      min-height: 100px;
    }

    .sp-textarea-field__inner {
      padding: 20px 14px;
      font-size: 18px;
      min-height: 60px;
    }

    .sp-textarea-field__label {
      left: 14px;
      top: 20px;
      font-size: 18px;

      &--floating {
        transform: translateY(-55%) scale(0.9);
      }
    }

    .sp-textarea-field__prefix,
    .sp-textarea-field__suffix {
      padding-top: 20px;
    }

    .sp-textarea-field__prefix {
      padding-left: 14px;
      padding-right: 7px;
    }

    .sp-textarea-field__suffix {
      padding-right: 14px;
      padding-left: 7px;
    }

    .sp-textarea-field__count {
      padding-bottom: 20px;
    }
  }

  // ===== 调整大小变体 =====
  &--resize-none {
    .sp-textarea-field__inner {
      resize: none;
    }

    // 无调整大小时隐藏拖拽图标区域
    .sp-textarea-field__wrapper::after {
      display: none;
    }
  }

  &--resize-both {
    .sp-textarea-field__inner {
      resize: both;
    }
  }

  &--resize-horizontal {
    .sp-textarea-field__inner {
      resize: horizontal;
    }
  }

  &--resize-vertical {
    .sp-textarea-field__inner {
      resize: vertical;
    }
  }

  // ===== 有后缀时的特殊处理 =====
  &--has-suffix {
    .sp-textarea-field__wrapper {
      // 为后缀内容调整布局
      position: relative;
    }

    .sp-textarea-field__inner {
      // 为后缀内容预留空间，避免重叠
      padding-right: 60px; // 增加右侧内边距

      // 确保拖拽手柄在正确位置
      &::-webkit-resizer {
        position: relative;
        z-index: 10; // 确保拖拽手柄在最上层
      }
    }

    .sp-textarea-field__suffix {
      // 绝对定位，避免影响文本域布局
      position: absolute;
      top: 16px;
      right: 20px; // 留出拖拽图标空间
      bottom: 20px;
      width: auto;
      max-width: 100px;
      padding: 0;

      // 垂直排列，从上到下
      flex-direction: column;
      align-items: flex-end;
      justify-content: flex-start;
      gap: 8px;

      // 不阻止拖拽操作
      pointer-events: none;

      // 后缀元素可交互
      > * {
        pointer-events: auto;
      }
    }
  }

  // ===== 自动调整大小 =====
  &--autosize {
    .sp-textarea-field__inner {
      resize: none;
      overflow: hidden;
    }
  }

  // ===== 变体样式 =====

  // 默认变体
  &--default {
    .sp-textarea-field__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 6px;
      background: var(--sp-color-white);
    }

    .sp-textarea-field__label {
      background: var(--sp-color-white);
      padding: 0 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // ===== 状态样式 =====
  &--error {
    .sp-textarea-field__wrapper {
      border-color: $color-danger !important;
    }

    .sp-textarea-field__label {
      color: $color-danger !important;
    }

    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2) !important;
      }
    }
  }

  &--disabled {
    .sp-textarea-field__wrapper {
      background: $background-color-disabled;
      border-color: $border-color-disabled;
      cursor: not-allowed;
    }

    .sp-textarea-field__label {
      color: $color-text-disabled;
    }
  }

  // ===== 发光效果 =====
  &--effect-glow {
    &.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2),
          0 4px 12px rgba($color-primary, 0.15);
      }
    }

    &.sp-textarea-field--error.sp-textarea-field--focused {
      .sp-textarea-field__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2),
          0 4px 12px rgba($color-danger, 0.15);
      }
    }
  }
}

// ===== 动画 =====
@keyframes sp-textarea-field-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(350%);
  }
}

// ===== 消息过渡动画 =====
.sp-textarea-field-message-enter-active,
.sp-textarea-field-message-leave-active {
  transition: all 0.3s ease;
}

.sp-textarea-field-message-enter-from,
.sp-textarea-field-message-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
