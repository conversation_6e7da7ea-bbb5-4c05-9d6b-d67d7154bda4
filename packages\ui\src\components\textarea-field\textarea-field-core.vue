<!--
  TextareaFieldCore.vue - TextareaField 核心实现
  结合了 Textarea 的所有功能和 FormItem 的表单集成，并添加浮动标签效果
-->

<template>
  <div :class="rootClasses">
    <!-- 文本域包装器 -->
    <div
      ref="wrapperRef"
      :class="wrapperClasses"
      @click="handleWrapperClick"
    >
      <!-- 浮动标签 -->
      <label
        v-if="label"
        :class="labelClasses"
        :for="textareaId"
        @click="handleLabelClick"
      >
        <span
          v-if="required"
          class="sp-textarea-field__label-asterisk"
        >
          *
        </span>
        {{ label }}
      </label>

      <!-- 前缀区域 -->
      <div
        v-if="slots.prefix || prefixIcon"
        :class="prefixClasses"
        @click.stop
        @mousedown.prevent
      >
        <sp-icon
          v-if="prefixIcon"
          :name="prefixIcon"
          :size="iconSize"
          :class="prefixIconClasses"
        />
        <slot name="prefix"></slot>
      </div>

      <!-- 文本域 -->
      <textarea
        :id="textareaId"
        ref="textareaRef"
        :value="value"
        :placeholder="computedPlaceholder"
        :disabled="computedDisabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :rows="rows || 3"
        :cols="cols"
        :class="textareaClasses"
        :style="textareaStyle"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />

      <!-- 后缀区域 -->
      <div
        v-if="showSuffixOverride"
        :class="suffixClasses"
        @click.stop
        @mousedown.prevent
      >
        <!-- 清除按钮 -->
        <sp-icon
          v-if="showClearIcon"
          name="CloseCircle"
          :size="iconSize"
          clickable
          :class="clearIconClasses"
          @click.stop.prevent="handleClear"
          @mousedown.stop.prevent
        />

        <!-- 字数统计 -->
        <span
          v-if="showWordLimit"
          :class="wordCountClasses"
        >
          {{ wordCount }}/{{ maxlength }}
        </span>

        <!-- 自定义后缀图标 -->
        <sp-icon
          v-if="suffixIcon"
          :name="suffixIcon"
          :size="iconSize"
          :class="suffixIconClasses"
        />

        <!-- 后缀插槽 -->
        <slot name="suffix"></slot>
      </div>
    </div>

    <!-- 加载动画条 -->
    <div
      v-if="loading"
      :class="loadingBarClasses"
    >
      <div :class="loadingProgressClasses"></div>
    </div>

    <!-- 帮助文本和验证消息 -->
    <transition name="sp-textarea-field-message">
      <div
        v-if="shouldShowMessage"
        :class="messageClasses"
      >
        {{ currentMessage }}
      </div>
    </transition>

    <div
      v-if="helperText && !shouldShowMessage"
      :class="helperTextClasses"
    >
      {{ helperText }}
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, inject, useSlots, useId, ref, watch, nextTick } from 'vue'
  import { useField } from 'vee-validate'
  import SpIcon from '../icon/Icon.vue'
  import { useInputLogic } from '../../composables'
  import { useTextareaFieldStyles } from './useTextareaFieldStyles'
  import type { TextareaFieldProps, TextareaFieldEmits } from './types'
  import type { FormContext } from '../Form/types'

  // ===== Props 和 Emits =====
  const props = withDefaults(defineProps<TextareaFieldProps>(), {
    value: '',
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    clearable: false,
    showWordLimit: false,
    error: false,
    loading: false,
    required: false,
    showMessage: true,
    persistentLabel: false,
    rows: 3,
    resize: 'vertical',
    autosize: false,
  })

  const emit = defineEmits<TextareaFieldEmits>()
  const slots = useSlots()

  // ===== 表单集成 =====
  const formContext = inject<FormContext>('spForm', {})

  // VeeValidate 字段集成
  const veeField = useField(props.name, props.rules as any, {
    validateOnValueUpdate: false,
  })

  // ===== 使用 Input 逻辑 Composable =====
  const {
    // 引用
    wrapperRef,
    inputRef: textareaRef, // 重命名为 textareaRef

    // 状态
    isFocused,

    // 计算属性
    iconSize,
    hasValue,
    wordCount,
    computedDisabled,

    // 显示逻辑
    showClearIcon,
    showSuffix,

    // 事件处理
    handleInput: originalHandleInput,
    handleChange: originalHandleChange,
    handleFocus: originalHandleFocus,
    handleBlur: originalHandleBlur,
    handleKeydown,
    handleWrapperClick,
    handleClear: originalHandleClear,

    // 方法
    focus,
    blur,
    select,
    clear,
  } = useInputLogic(props, emit)

  // ===== Textarea 特有功能 =====
  // 自动调整高度功能
  const adjustHeight = () => {
    if (!props.autosize || !textareaRef.value) return

    const textarea = textareaRef.value
    // 重置高度以获取正确的 scrollHeight
    textarea.style.height = 'auto'

    let height = textarea.scrollHeight

    // 处理 autosize 配置
    if (typeof props.autosize === 'object') {
      const { minRows = 1, maxRows } = props.autosize
      const lineHeight = parseInt(getComputedStyle(textarea).lineHeight) || 20

      if (minRows) {
        height = Math.max(height, minRows * lineHeight)
      }
      if (maxRows) {
        height = Math.min(height, maxRows * lineHeight)
      }
    }

    textarea.style.height = `${height}px`
  }

  // 计算 textarea 样式
  const textareaStyle = computed(() => ({
    overflow: props.autosize ? 'hidden' : 'auto',
    resize: props.resize || 'vertical',
  }))

  // ===== 浮动标签逻辑 =====
  const textareaId = useId()

  const isLabelFloating = computed(() => {
    return props.persistentLabel || isFocused.value || hasValue.value
  })

  const computedPlaceholder = computed(() => {
    // 当标签浮动时显示 placeholder，否则不显示（避免重复）
    return isLabelFloating.value ? props.placeholder : ''
  })

  // ===== 重写 showSuffix 逻辑，包含插槽检查 =====
  const showSuffixOverride = computed(() => {
    return (
      showClearIcon.value ||
      props.showWordLimit ||
      props.suffixIcon ||
      !!slots.suffix // 检查是否存在 suffix 插槽
    )
  })

  // ===== 验证状态管理 =====
  const validateState = computed(() => {
    if (props.error) return 'error'
    if (veeField?.meta.valid === false && veeField?.meta.touched) return 'error'
    if (veeField?.meta.valid === true && veeField?.meta.touched)
      return 'success'
    return ''
  })

  const currentMessage = computed(() => {
    return veeField?.errorMessage.value || ''
  })

  const shouldShowMessage = computed(() => {
    return props.showMessage && !!currentMessage.value
  })

  // ===== 增强事件处理（集成表单验证） =====
  const handleInput = (event: Event) => {
    originalHandleInput(event)
    // 同步到 VeeValidate
    const target = event.target as HTMLTextAreaElement
    veeField?.setValue(target.value === '' ? undefined : target.value)
  }

  const handleChange = (event: Event) => {
    originalHandleChange(event)
    // 触发验证
    veeField?.validate()
  }

  const handleFocus = (event: FocusEvent) => {
    originalHandleFocus(event)
  }

  const handleBlur = async (event: FocusEvent) => {
    originalHandleBlur(event)
    // 触发验证
    veeField?.handleBlur()
    await veeField?.validate()
  }

  const handleClear = () => {
    originalHandleClear()
    veeField?.setValue(undefined)
  }

  const handleLabelClick = () => {
    if (!computedDisabled.value && !props.readonly) {
      focus()
    }
  }

  // ===== 同步外部 value 到 VeeValidate =====
  watch(
    () => props.value,
    newValue => {
      if (veeField?.value.value !== newValue) {
        veeField?.setValue(newValue)
      }
    },
    { immediate: true }
  )

  // ===== 使用样式 Composable =====
  const {
    rootClasses,
    wrapperClasses,
    labelClasses,
    textareaClasses,
    prefixClasses,
    suffixClasses,
    prefixIconClasses,
    suffixIconClasses,
    clearIconClasses,
    wordCountClasses,
    loadingBarClasses,
    loadingProgressClasses,
    helperTextClasses,
    messageClasses,
  } = useTextareaFieldStyles(props, {
    computedDisabled: computed(() => computedDisabled.value),
    isFocused,
    hasValue: computed(() => hasValue.value),
    isLabelFloating,
    validateState,
  })

  // ===== 表单方法 =====
  const validate = async (): Promise<boolean> => {
    const result = await veeField?.validate()
    return result?.valid || false
  }

  const resetField = () => {
    veeField?.resetField()
    emit('update:value', undefined)
  }

  const clearValidate = () => {
    veeField?.setErrors([])
  }

  // ===== 暴露方法 =====
  defineExpose({
    focus,
    blur,
    select,
    clear,
    validate,
    resetField,
    clearValidate,
    adjustHeight,
    get textarea() {
      return textareaRef.value || null
    },
    get wrapper() {
      return wrapperRef.value || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'TextareaFieldCore',
  }
</script>
