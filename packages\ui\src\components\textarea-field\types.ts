/**
 * TextareaField 组件类型定义
 * 继承 Textarea 组件所有功能，并添加表单集成和浮动标签
 */

import type { TextareaProps, TextareaEmits } from '../textarea/types'

/** TextareaField 专有属性 */
export interface TextareaFieldOwnProps {
  /** 标签文本 - 支持浮动动画 */
  label?: string
  /** 表单字段名 - 用于表单验证 */
  name: string
  /** 验证规则 */
  rules?: any
  /** 是否必填 */
  required?: boolean
  /** 是否显示验证消息 */
  showMessage?: boolean
  /** 帮助文本 */
  helperText?: string
  /** 标签是否始终浮动（不回落） */
  persistentLabel?: boolean
}

/** TextareaField 完整属性，继承 Textarea 的所有属性 */
export interface TextareaFieldProps
  extends Omit<TextareaProps, 'validateState' | 'validateMessage'>,
    TextareaFieldOwnProps {}

/** TextareaField 事件 */
export interface TextareaFieldEmits extends TextareaEmits {
  /** 验证事件 */
  (e: 'validate', name: string, isValid: boolean, message: string): void
}

/** TextareaField 实例方法 */
export interface TextareaFieldInstance {
  /** 使文本域获得焦点 */
  focus: () => void
  /** 使文本域失去焦点 */
  blur: () => void
  /** 选中文本域中的文字 */
  select: () => void
  /** 清空文本域 */
  clear: () => void
  /** 验证字段 */
  validate: () => Promise<boolean>
  /** 重置字段 */
  resetField: () => void
  /** 清除验证 */
  clearValidate: () => void
  /** 调整文本域高度（autosize 模式下） */
  adjustHeight: () => void
  /** 文本域元素引用 */
  textarea: HTMLTextAreaElement | null
  /** 包装器元素引用 */
  wrapper: HTMLDivElement | null
}

/** TextareaField 默认属性 */
export const textareaFieldPropsDefaults: Partial<TextareaFieldProps> = {
  value: '',
  variant: 'default',
  effect: 'none',
  size: 'medium',
  disabled: false,
  readonly: false,
  clearable: false,
  showWordLimit: false,
  error: false,
  loading: false,
  required: false,
  showMessage: true,
  persistentLabel: false,
  rows: 3,
  resize: 'vertical',
  autosize: false,
}
