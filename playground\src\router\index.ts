import { createRouter, createWebHistory } from 'vue-router'
import { SUPPORTED_LOCALES, DEFAULT_LOCALE } from '../i18n'
import type { Locale } from '../i18n'
// import { componentRoutes } from './components'

// 路由组件
const Home = () => import('../views/Home.vue')
const VeeValidateDemo = () => import('../pages/VeeValidateDemo.vue')
const FormLabelControlDemo = () => import('../pages/FormLabelControlDemo.vue')
const SelectDemo = () => import('../pages/SelectDemo.vue')
const SelectMultipleDemo = () => import('../pages/SelectMultipleDemo.vue')
const SelectMultipleTest = () => import('../pages/SelectMultipleTest.vue')
const SelectTestDemo = () => import('../pages/SelectTestDemo.vue')
const SimpleSelectTest = () => import('../pages/SimpleSelectTest.vue')
const LinkedSelectDemo = () => import('../pages/LinkedSelectDemo.vue')
const NewSelectDemo = () => import('../views/SelectDemo.vue')
const ButtonDemo = () => import('../pages/ButtonDemo.vue')
const LinkageDemo = () => import('../pages/LinkageDemo.vue')
const UnstyledDemo = () => import('../views/UnstyledDemo.vue')
const InputUnstyledDemo = () => import('../views/InputUnstyledDemo.vue')
const InputFocusTest = () => import('../pages/InputFocusTest.vue')
const InputEnhancedDemo = () => import('../pages/InputEnhancedDemo.vue')
const InputIconTest = () => import('../views/InputIconTest.vue')
const InputV2Demo = () => import('../views/InputV2Demo.vue')
const InputComponentsDemo = () => import('../pages/InputComponentsDemo.vue')
const IconDemo = () => import('../pages/IconDemo.vue')
const InputDisabledTest = () => import('../pages/InputDisabledTest.vue')
const InputNumberTest = () => import('../pages/InputNumberTest.vue')
const InputWrapperTest = () => import('../pages/InputWrapperTest.vue')
const InputDemo = () => import('../pages/InputDemo.vue')
const MenuDemo = () => import('../views/MenuDemo.vue')
const ListDemo = () => import('../views/ListDemo.vue')
const ListVariantDemo = () => import('../views/ListVariantDemo.vue')
const SelectSizeTest = () => import('../views/SelectSizeTest.vue')
const TagDemo = () => import('../views/TagDemo.vue')
const DropdownDemo = () => import('../views/DropdownDemo.vue')
const SelectTagTest = () => import('../pages/SelectTagTest.vue')
const SimpleTagTest = () => import('../pages/SimpleTagTest.vue')
const ScrollbarDemo = () => import('../pages/ScrollbarDemo.vue')
const ScrollbarSimpleTest = () => import('../pages/ScrollbarSimpleTest.vue')
const InputNewDemo = () => import('../pages/InputNewDemo.vue')
const InputFieldDemo = () => import('../pages/InputFieldDemo.vue')
const TextareaDemo = () => import('../pages/TextareaDemo.vue')
const SelectFieldDemo = () => import('../pages/SelectFieldDemo.vue')
const SelectFieldClearTest = () => import('../pages/SelectFieldClearTest.vue')
const SelectNewDemo = () => import('../pages/SelectNewDemo.vue')

// 路由配置
const routes = [
  {
    path: '/',
    redirect: `/${DEFAULT_LOCALE}`,
  },
  {
    path: '/:locale/InputWrapperTest',
    component: InputWrapperTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/InputWrapperTest`
      }
    },
  },
  {
    path: '/:locale',
    component: Home,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}`
      }
    },
  },
  // 添加组件展示路由
  // ...componentRoutes,
  {
    path: '/:locale/button-demo',
    component: ButtonDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/button-demo`
      }
    },
  },
  {
    path: '/:locale/linkage-demo',
    component: LinkageDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/linkage-demo`
      }
    },
  },
  {
    path: '/:locale/vee-validate-demo',
    component: VeeValidateDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/vee-validate-demo`
      }
    },
  },
  {
    path: '/:locale/form-label-control-demo',
    component: FormLabelControlDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/form-label-control-demo`
      }
    },
  },
  {
    path: '/:locale/select-demo',
    component: SelectDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/select-demo`
      }
    },
  },
  {
    path: '/:locale/select-multiple-demo',
    component: SelectMultipleDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/select-multiple-demo`
      }
    },
  },
  {
    path: '/:locale/select-multiple-test',
    component: SelectMultipleTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/select-multiple-test`
      }
    },
  },
  {
    path: '/:locale/LinkedSelectDemo',
    component: LinkedSelectDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/LinkedSelectDemo`
      }
    },
  },
  {
    path: '/:locale/unstyled-demo',
    component: UnstyledDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/unstyled-demo`
      }
    },
  },
  {
    path: '/:locale/input-unstyled-demo',
    component: InputUnstyledDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-unstyled-demo`
      }
    },
  },
  {
    path: '/:locale/input-focus-test',
    component: InputFocusTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-focus-test`
      }
    },
  },
  {
    path: '/:locale/input-enhanced-demo',
    component: InputEnhancedDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-enhanced-demo`
      }
    },
  },
  {
    path: '/:locale/input-icon-test',
    component: InputIconTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-icon-test`
      }
    },
  },
  {
    path: '/:locale/input-v2-demo',
    component: InputV2Demo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-v2-demo`
      }
    },
  },
  {
    path: '/:locale/input-components-demo',
    component: InputComponentsDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-components-demo`
      }
    },
  },
  {
    path: '/:locale/input-demo',
    component: InputDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-demo`
      }
    },
  },
  {
    path: '/:locale/icon-demo',
    component: IconDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/icon-demo`
      }
    },
  },
  {
    path: '/:locale/maxlength-test',
    component: () => import('../pages/MaxlengthTest.vue'),
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/maxlength-test`
      }
    },
  },
  {
    path: '/:locale/input-disabled-test',
    component: InputDisabledTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-disabled-test`
      }
    },
  },
  {
    path: '/:locale/input-number-test',
    component: InputNumberTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-number-test`
      }
    },
  },
  {
    path: '/:locale/simple-select-test',
    component: SimpleSelectTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/simple-select-test`
      }
    },
  },
  {
    path: '/:locale/menu-demo',
    component: MenuDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/menu-demo`
      }
    },
  },
  {
    path: '/:locale/list-demo',
    component: ListDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/list-demo`
      }
    },
  },
  {
    path: '/:locale/list-variant-demo',
    component: ListVariantDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/list-variant-demo`
      }
    },
  },
  {
    path: '/:locale/select-size-test',
    component: SelectSizeTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/select-size-test`
      }
    },
  },
  {
    path: '/:locale/tag-demo',
    component: TagDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/tag-demo`
      }
    },
  },
  {
    path: '/:locale/dropdown-demo',
    component: DropdownDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/dropdown-demo`
      }
    },
  },
  {
    path: '/:locale/select-demo-new',
    component: NewSelectDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/select-demo-new`
      }
    },
  },
  {
    path: '/:locale/select-tag-test',
    component: SelectTagTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/select-tag-test`
      }
    },
  },
  {
    path: '/:locale/simple-tag-test',
    component: SimpleTagTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/simple-tag-test`
      }
    },
  },
  {
    path: '/:locale/scrollbar-demo',
    component: ScrollbarDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/scrollbar-demo`
      }
    },
  },
  {
    path: '/:locale/scrollbar-simple-test',
    component: ScrollbarSimpleTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/scrollbar-simple-test`
      }
    },
  },
  {
    path: '/:locale/input-new-demo',
    component: InputNewDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-new-demo`
      }
    },
  },
  {
    path: '/:locale/input-field-demo',
    component: InputFieldDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/input-field-demo`
      }
    },
  },
  {
    path: '/:locale/textarea-demo',
    component: TextareaDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/textarea-demo`
      }
    },
  },
  {
    path: '/:locale/select-field-demo',
    component: SelectFieldDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/select-field-demo`
      }
    },
  },
  {
    path: '/:locale/select-field-clear-test',
    component: SelectFieldClearTest,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/select-field-clear-test`
      }
    },
  },
  {
    path: '/:locale/select-new-demo',
    component: SelectNewDemo,
    beforeEnter: (to: any) => {
      const locale = to.params.locale as Locale
      if (!SUPPORTED_LOCALES.includes(locale)) {
        return `/${DEFAULT_LOCALE}/select-new-demo`
      }
    },
  },
  // 简化路由（不需要语言参数）
  {
    path: '/select-field',
    redirect: '/zh-CN/select-field-demo',
  },
  {
    path: '/select-field-clear-test',
    redirect: '/zh-CN/select-field-clear-test',
  },
]

// 创建路由实例
export const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 路由守卫：同步路由语言�?i18n 语言
router.beforeEach(to => {
  const locale = to.params.locale as Locale
  if (locale && SUPPORTED_LOCALES.includes(locale)) {
    // 这里会在 main.ts 中设�?i18n 的语言
    localStorage.setItem('speed-ui-locale', locale)
    document.querySelector('html')?.setAttribute('lang', locale)
  }
})
